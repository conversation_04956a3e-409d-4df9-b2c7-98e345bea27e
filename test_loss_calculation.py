#!/usr/bin/env python3
"""
测试损失计算的张量形状
"""

import torch
import torch.nn as nn

def test_loss_shapes():
    """测试损失计算中的张量形状"""
    
    print("=== 损失计算张量形状测试 ===")
    
    # 模拟参数
    batch_size = 2
    seq_len = 10
    num_rels = 24
    
    # 创建模拟数据
    print(f"批次大小: {batch_size}")
    print(f"序列长度: {seq_len}")
    print(f"关系数量: {num_rels}")
    print()
    
    # Subject预测和标签 (batch_size, seq_len)
    sub_heads_pred = torch.randn(batch_size, seq_len)
    sub_heads_gold = torch.randint(0, 2, (batch_size, seq_len)).float()
    
    # Object预测和标签 (batch_size, seq_len, num_rels)
    obj_heads_pred = torch.randn(batch_size, seq_len, num_rels)
    obj_heads_gold = torch.randint(0, 2, (batch_size, seq_len, num_rels)).float()
    
    # Attention mask (batch_size, seq_len)
    mask = torch.ones(batch_size, seq_len)
    mask[:, 8:] = 0  # 模拟padding
    
    print("张量形状:")
    print(f"  sub_heads_pred: {sub_heads_pred.shape}")
    print(f"  sub_heads_gold: {sub_heads_gold.shape}")
    print(f"  obj_heads_pred: {obj_heads_pred.shape}")
    print(f"  obj_heads_gold: {obj_heads_gold.shape}")
    print(f"  mask: {mask.shape}")
    print()
    
    # 损失函数
    criterion = nn.BCEWithLogitsLoss(reduction='none')
    
    # Subject损失计算
    print("Subject损失计算:")
    sub_heads_loss = criterion(sub_heads_pred, sub_heads_gold)
    print(f"  原始损失形状: {sub_heads_loss.shape}")
    
    masked_loss = sub_heads_loss * mask
    print(f"  应用mask后: {masked_loss.shape}")
    
    final_loss = masked_loss.sum() / mask.sum()
    print(f"  最终损失: {final_loss.item():.4f}")
    print()
    
    # Object损失计算
    print("Object损失计算:")
    obj_heads_loss = criterion(obj_heads_pred, obj_heads_gold)
    print(f"  原始损失形状: {obj_heads_loss.shape}")
    
    # 方法1: sum(dim=-1) - 在关系维度上求和
    summed_loss = obj_heads_loss.sum(dim=-1)
    print(f"  关系维度求和后: {summed_loss.shape}")
    
    masked_obj_loss = summed_loss * mask
    print(f"  应用mask后: {masked_obj_loss.shape}")
    
    final_obj_loss = masked_obj_loss.sum() / mask.sum()
    print(f"  最终object损失: {final_obj_loss.item():.4f}")
    print()
    
    # 验证维度错误
    print("验证维度错误:")
    try:
        # 这会导致错误，因为obj_heads_loss只有3个维度
        wrong_sum = obj_heads_loss.sum(dim=2)  # 等同于dim=-1，应该正常
        print(f"  dim=2求和: {wrong_sum.shape} ✓")
    except Exception as e:
        print(f"  dim=2求和错误: {e}")
    
    try:
        # 这会导致错误
        wrong_sum = obj_heads_loss.sum(dim=3)  # 超出维度范围
        print(f"  dim=3求和: {wrong_sum.shape}")
    except Exception as e:
        print(f"  dim=3求和错误: {e} ✗")
    
    print("\n=== 测试完成 ===")

def test_actual_model_shapes():
    """测试实际模型的输出形状"""
    try:
        from casrel_torch import CasRelModel
        from transformers import BertTokenizer
        
        print("\n=== 实际模型形状测试 ===")
        
        tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
        model = CasRelModel('bert-base-cased', 24)
        model.eval()
        
        # 创建模拟输入
        batch_size = 2
        seq_len = 32
        
        input_ids = torch.randint(1, 1000, (batch_size, seq_len))
        attention_mask = torch.ones(batch_size, seq_len)
        sub_head_idx = torch.randint(0, seq_len, (batch_size, 1))
        sub_tail_idx = torch.randint(0, seq_len, (batch_size, 1))
        
        print(f"输入形状:")
        print(f"  input_ids: {input_ids.shape}")
        print(f"  attention_mask: {attention_mask.shape}")
        print(f"  sub_head_idx: {sub_head_idx.shape}")
        print(f"  sub_tail_idx: {sub_tail_idx.shape}")
        
        with torch.no_grad():
            sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred = model(
                input_ids, attention_mask, sub_head_idx, sub_tail_idx)
        
        print(f"\n模型输出形状:")
        print(f"  sub_heads_pred: {sub_heads_pred.shape}")
        print(f"  sub_tails_pred: {sub_tails_pred.shape}")
        print(f"  obj_heads_pred: {obj_heads_pred.shape}")
        print(f"  obj_tails_pred: {obj_tails_pred.shape}")
        
    except ImportError as e:
        print(f"\n无法导入模型: {e}")

if __name__ == "__main__":
    test_loss_shapes()
    test_actual_model_shapes()
