#!/usr/bin/env python3
"""
测试数据加载功能
"""

import json

def test_data_loading():
    """测试数据加载"""
    
    print("=== 数据加载测试 ===")
    
    # 测试关系数据
    print("1. 测试关系数据...")
    with open('data/NYT/rel2id.json', 'r', encoding='utf-8') as f:
        rel_data = json.load(f)
    
    id2rel = {int(k): v for k, v in rel_data[0].items()}
    rel2id = rel_data[1]
    
    print(f"   关系数量: {len(rel2id)}")
    print(f"   id2rel示例: {dict(list(id2rel.items())[:3])}")
    print(f"   rel2id示例: {dict(list(rel2id.items())[:3])}")
    
    # 测试训练数据
    print("\n2. 测试训练数据...")
    with open('data/NYT/train_triples.json', 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    
    print(f"   训练样本数: {len(train_data)}")
    print(f"   第一个样本: {train_data[0]}")
    
    # 测试开发数据
    print("\n3. 测试开发数据...")
    with open('data/NYT/dev_triples.json', 'r', encoding='utf-8') as f:
        dev_data = json.load(f)
    
    print(f"   开发样本数: {len(dev_data)}")
    
    # 测试测试数据
    print("\n4. 测试测试数据...")
    with open('data/NYT/test_triples.json', 'r', encoding='utf-8') as f:
        test_data = json.load(f)
    
    print(f"   测试样本数: {len(test_data)}")
    
    # 验证数据格式
    print("\n5. 验证数据格式...")
    sample = train_data[0]
    print(f"   文本字段: {'text' in sample}")
    print(f"   三元组字段: {'triple_list' in sample}")
    print(f"   三元组数量: {len(sample['triple_list'])}")
    print(f"   三元组示例: {sample['triple_list'][0]}")
    
    # 检查关系是否在rel2id中
    sample_rel = sample['triple_list'][0][1]
    print(f"   示例关系: {sample_rel}")
    print(f"   关系在rel2id中: {sample_rel in rel2id}")
    
    if sample_rel in rel2id:
        print(f"   关系ID: {rel2id[sample_rel]}")
    
    print("\n=== 数据加载测试完成 ===")
    return True

if __name__ == "__main__":
    test_data_loading()
