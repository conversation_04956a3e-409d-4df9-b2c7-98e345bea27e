#! -*- coding:utf-8 -*-
"""
CasRel PyTorch Implementation - Corrected & Optimized Version with Offset Mapping
A clean, easy-to-understand implementation of CasRel model using robust entity mapping.
"""
import torch
import torch.nn as nn
import json
import numpy as np
# --- 修改: 导入 BertTokenizerFast ---
from transformers import BertModel, BertTokenizerFast
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import argparse
import os
import random
import re

class CasRelModel(nn.Module):
    """Simplified CasRel Model"""
    def __init__(self, bert_model, num_rels):
        super().__init__()
        self.bert = BertModel.from_pretrained(bert_model)
        hidden_size = self.bert.config.hidden_size
        self.sub_heads = nn.Linear(hidden_size, 1)
        self.sub_tails = nn.Linear(hidden_size, 1)
        self.obj_heads = nn.Linear(hidden_size, num_rels)
        self.obj_tails = nn.Linear(hidden_size, num_rels)
        
    def forward(self, input_ids, attention_mask, sub_head_idx=None, sub_tail_idx=None):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        sequence_output = outputs.last_hidden_state
        sub_heads_logits = self.sub_heads(sequence_output).squeeze(-1)
        sub_tails_logits = self.sub_tails(sequence_output).squeeze(-1)
        sub_heads_pred = torch.sigmoid(sub_heads_logits)
        sub_tails_pred = torch.sigmoid(sub_tails_logits)
        
        if sub_head_idx is not None and sub_tail_idx is not None:
            sub_head_features = torch.gather(sequence_output, 1, sub_head_idx.unsqueeze(-1).expand(-1, -1, sequence_output.size(-1)))
            sub_tail_features = torch.gather(sequence_output, 1, sub_tail_idx.unsqueeze(-1).expand(-1, -1, sequence_output.size(-1)))
            sub_features = (sub_head_features + sub_tail_features) / 2
            enhanced_output = sequence_output + sub_features
            obj_heads_logits = self.obj_heads(enhanced_output)
            obj_tails_logits = self.obj_tails(enhanced_output)
            obj_heads_pred = torch.sigmoid(obj_heads_logits)
            obj_tails_pred = torch.sigmoid(obj_tails_logits)
            return sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred
        return sub_heads_pred, sub_tails_pred

class CasRelDataset(Dataset):
    """
    Dataset class for CasRel, using offset_mapping for robust entity span detection.
    """
    def __init__(self, data, tokenizer, rel2id, max_len=128):
        self.data = data
        self.tokenizer = tokenizer
        self.rel2id = rel2id
        self.max_len = max_len
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = item['text']

        # Tokenize text and get offset_mapping (Requires FastTokenizer)
        encoding = self.tokenizer(
            text, 
            max_length=self.max_len, 
            truncation=True,
            padding='max_length', 
            return_tensors='pt',
            return_offsets_mapping=True # <-- 关键参数
        )

        input_ids = encoding['input_ids'].squeeze(0)
        attention_mask = encoding['attention_mask'].squeeze(0)
        # offset_mapping 仅在 CPU 上操作通常更方便
        offset_mapping = encoding['offset_mapping'].squeeze(0).numpy() # Shape: (max_len, 2)

        # Initialize labels
        seq_len = input_ids.size(0)
        sub_heads = torch.zeros(seq_len)
        sub_tails = torch.zeros(seq_len)
        obj_heads = torch.zeros(seq_len, len(self.rel2id))
        obj_tails = torch.zeros(seq_len, len(self.rel2id))
        
        subjects = {}
        for triple in item['triple_list']:
            # triple format: [subj_text, [subj_start, subj_end], rel, obj_text, [obj_start, obj_end]]
            subj_span = triple[1] # [start_char, end_char]
            obj_span = triple[4]  # [start_char, end_char]

            # Convert character spans to token spans using offset_mapping
            sub_token_span = self.char_span_to_token_span(offset_mapping, subj_span)
            obj_token_span = self.char_span_to_token_span(offset_mapping, obj_span)

            if sub_token_span and obj_token_span: # If spans are valid
                sub_start, sub_end = sub_token_span
                obj_start, obj_end = obj_token_span
                rel_id = self.rel2id[triple[2]] # relation is at index 2
                
                # Subject labels
                sub_heads[sub_start] = 1
                sub_tails[sub_end] = 1
                
                # Store for object labeling
                if (sub_start, sub_end) not in subjects:
                    subjects[(sub_start, sub_end)] = []
                subjects[(sub_start, sub_end)].append((obj_start, obj_end, rel_id))
        
        # Object labels (randomly select one subject)
        if subjects:
            sub_pos = random.choice(list(subjects.keys()))
            sub_head_idx = torch.tensor([sub_pos[0]])
            sub_tail_idx = torch.tensor([sub_pos[1]])
            for obj_start, obj_end, rel_id in subjects[sub_pos]:
                obj_heads[obj_start, rel_id] = 1
                obj_tails[obj_end, rel_id] = 1
        else: # Handle cases with no triples
            sub_head_idx = torch.tensor([0])
            sub_tail_idx = torch.tensor([0])
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'sub_heads': sub_heads,
            'sub_tails': sub_tails,
            'obj_heads': obj_heads,
            'obj_tails': obj_tails,
            'sub_head_idx': sub_head_idx,
            'sub_tail_idx': sub_tail_idx
        }

    def char_span_to_token_span(self, offset_mapping, char_span):
        """
        Converts a character-level span to a token-level span using the offset_mapping.
        """
        char_start, char_end = char_span
        token_start_index = -1
        token_end_index = -1

        for idx, (start, end) in enumerate(offset_mapping):
            # Skip special tokens and padding which have (0, 0) offset
            if start == 0 and end == 0:
                continue
            
            # Find the first token whose character span overlaps with the entity's start
            # We check if the token starts at or before the entity start AND ends after the entity start
            if token_start_index == -1 and start <= char_start < end:
                token_start_index = idx
            
            # Find the last token whose character span overlaps with the entity's end
            # We check if the token starts before the entity end AND ends at or after the entity end
            if start < char_end <= end:
                token_end_index = idx

        if token_start_index == -1 or token_end_index == -1 or token_start_index > token_end_index:
            return None # Span not found or invalid (e.g., due to truncation)
            
        return (token_start_index, token_end_index)

def load_data(data_path, rel_path):
    """
    Loads data and preprocesses it to include character-level spans for entities.
    """
    # Load relation data
    with open(rel_path, 'r', encoding='utf-8') as f:
        rel_data = json.load(f)
    id2rel = {int(k): v for k, v in rel_data[0].items()}
    rel2id = rel_data[1]

    # Load and preprocess main data
    processed_data = []
    with open(data_path, 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
        for item in tqdm(raw_data, desc=f"Preprocessing {os.path.basename(data_path)}"):
            text = item['text']
            new_triple_list = []
            
            for triple in item['triple_list']:
                subj, rel, obj = triple
                
                # Find character spans. 
                # NOTE: This simple `find` is a basic approach. It might fail if the entity text 
                # appears multiple times or if there are normalization differences.
                subj_char_start = text.find(subj)
                obj_char_start = text.find(obj)

                if subj_char_start != -1 and obj_char_start != -1:
                    subj_char_end = subj_char_start + len(subj)
                    obj_char_end = obj_char_start + len(obj)
                    
                    # New format: [subj_text, [start, end], rel, obj_text, [start, end]]
                    new_triple_list.append([
                        subj, [subj_char_start, subj_char_end],
                        rel,
                        obj, [obj_char_start, obj_char_end]
                    ])
                # else:
                    # print(f"Warning: Could not find entity span. Triple: {triple}, Text: {text}")
                    
            if new_triple_list: # Only add items that have at least one valid triple
                processed_data.append({
                    'text': text,
                    'triple_list': new_triple_list
                })
    
    return processed_data, id2rel, rel2id

def extract_triples(model, tokenizer, text, id2rel, device, threshold=0.5):
    # (函数实现与之前相同，批处理优化)
    model.eval()
    # 注意：这里不需要 return_offsets_mapping，因为我们只关心模型的预测索引
    encoding = tokenizer(text, return_tensors='pt', max_length=128, truncation=True, padding='max_length')
    input_ids = encoding['input_ids'].to(device)
    attention_mask = encoding['attention_mask'].to(device)
    with torch.no_grad():
        sub_heads_pred, sub_tails_pred = model(input_ids, attention_mask)
        sub_heads = torch.where(sub_heads_pred[0] > threshold)[0]
        sub_tails = torch.where(sub_tails_pred[0] > threshold)[0]
        subjects = []
        for h in sub_heads:
            matching_tails = sub_tails[sub_tails >= h]
            if len(matching_tails) > 0:
                t = matching_tails[0]
                subjects.append((h.item(), t.item()))
        if not subjects: return []
        triples = []
        num_subjects = len(subjects)
        input_ids_batch = input_ids.repeat(num_subjects, 1)
        attention_mask_batch = attention_mask.repeat(num_subjects, 1)
        sub_head_idx_batch = torch.tensor([s[0] for s in subjects], device=device).unsqueeze(1)
        sub_tail_idx_batch = torch.tensor([s[1] for s in subjects], device=device).unsqueeze(1)
        _, _, obj_heads_pred_batch, obj_tails_pred_batch = model(input_ids_batch, attention_mask_batch, sub_head_idx_batch, sub_tail_idx_batch)
        for i, (sub_head, sub_tail) in enumerate(subjects):
            # 使用 decode 还原文本
            sub_text = tokenizer.decode(input_ids[0][sub_head:sub_tail+1], skip_special_tokens=True)
            obj_heads_pred, obj_tails_pred = obj_heads_pred_batch[i], obj_tails_pred_batch[i]
            obj_heads = torch.where(obj_heads_pred > threshold)
            obj_tails = torch.where(obj_tails_pred > threshold)
            for oh, or_h in zip(obj_heads[0].cpu(), obj_heads[1].cpu()):
                for ot, or_t in zip(obj_tails[0].cpu(), obj_tails[1].cpu()):
                    if oh <= ot and or_h == or_t:
                        rel = id2rel[or_h.item()]
                        obj_text = tokenizer.decode(input_ids[0][oh:ot+1], skip_special_tokens=True)
                        triples.append((sub_text, rel, obj_text))
                        break
    return list(set(triples))

def calculate_metrics(pred_triples, gold_triples):
    # (函数实现与之前相同)
    pred_set = set(pred_triples)
    gold_set = set(gold_triples)
    tp = len(pred_set & gold_set)
    precision = tp / len(pred_set) if len(pred_set) > 0 else 0
    recall = tp / len(gold_set) if len(gold_set) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    return precision, recall, f1

def train_model(model, train_loader, dev_data, tokenizer, id2rel, device, epochs=10):
    # (函数实现与之前相同，损失函数已修复)
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-5)
    criterion = nn.BCELoss(reduction='none')
    best_f1 = 0
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs}')
        for batch in progress_bar:
            optimizer.zero_grad()
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            sub_heads_gold = batch['sub_heads'].to(device)
            sub_tails_gold = batch['sub_tails'].to(device)
            obj_heads_gold = batch['obj_heads'].to(device)
            obj_tails_gold = batch['obj_tails'].to(device)
            sub_head_idx = batch['sub_head_idx'].to(device)
            sub_tail_idx = batch['sub_tail_idx'].to(device)
            sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred = model(input_ids, attention_mask, sub_head_idx, sub_tail_idx)
            mask = attention_mask.float()
            
            # Loss calculation (Corrected with reduction='none' and masking)
            sub_heads_loss = (criterion(sub_heads_pred, sub_heads_gold) * mask).sum() / mask.sum()
            sub_tails_loss = (criterion(sub_tails_pred, sub_tails_gold) * mask).sum() / mask.sum()
            obj_mask = mask.unsqueeze(-1).expand_as(obj_heads_gold)
            obj_heads_loss = (criterion(obj_heads_pred, obj_heads_gold) * obj_mask).sum() / obj_mask.sum()
            obj_tails_loss = (criterion(obj_tails_pred, obj_tails_gold) * obj_mask).sum() / obj_mask.sum()
            loss = sub_heads_loss + sub_tails_loss + obj_heads_loss + obj_tails_loss
            
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}'})

        precision, recall, f1 = evaluate_model(model, dev_data, tokenizer, id2rel, device)
        print(f'\nEpoch {epoch+1} - Dev Set: P={precision:.4f}, R={recall:.4f}, F1={f1:.4f}')

        if f1 > best_f1:
            best_f1 = f1
            torch.save(model.state_dict(), 'best_casrel_model.pt')
            print(f'>>> New best F1 on dev set: {f1:.4f}. Model saved.')
        model.train()

def evaluate_model(model, test_data, tokenizer, id2rel, device):
    model.eval()
    all_pred_triples = []
    all_gold_triples = []
    for item in tqdm(test_data, desc='Evaluating'):
        text = item['text']
        # Convert preprocessed gold triples (with spans) back to simple (s, r, o) tuples for evaluation
        gold_triples_for_item = []
        for triple in item['triple_list']:
             # Format: [subj_text, [span], rel, obj_text, [span]]
             gold_triples_for_item.append((triple[0], triple[2], triple[3]))
        
        pred_triples = extract_triples(model, tokenizer, text, id2rel, device)
        all_pred_triples.extend(pred_triples)
        all_gold_triples.extend(gold_triples_for_item)
    return calculate_metrics(all_pred_triples, all_gold_triples)

def main():
    parser = argparse.ArgumentParser(description='CasRel PyTorch - Corrected & Optimized with Offset Mapping')
    parser.add_argument('--dataset', default='NYT', help='Dataset name (e.g., NYT, WebNLG)')
    parser.add_argument('--batch_size', default=8, type=int, help='Batch size for training')
    parser.add_argument('--epochs', default=20, type=int, help='Number of training epochs')
    parser.add_argument('--bert_model', default='bert-base-cased', type=str, help='Pretrained BERT model')
    parser.add_argument('--train', action='store_true', help='Flag to train the model')
    parser.add_argument('--test', action='store_true', help='Flag to evaluate the model on the test set')
    args = parser.parse_args()

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    dataset_path = f'data/{args.dataset}'
    rel_path = os.path.join(dataset_path, 'rel2id.json')
    
    print("Loading and preprocessing data (finding character spans)...")
    # Load and preprocess all data splits
    train_data, id2rel, rel2id = load_data(os.path.join(dataset_path, 'train_triples.json'), rel_path)
    dev_data, _, _ = load_data(os.path.join(dataset_path, 'dev_triples.json'), rel_path)
    test_data, _, _ = load_data(os.path.join(dataset_path, 'test_triples.json'), rel_path)

    print(f'Loaded and preprocessed {len(train_data)} train, {len(dev_data)} dev, {len(test_data)} test samples')
    print(f'Number of relations: {len(rel2id)}')

    # --- 修改: 使用 BertTokenizerFast ---
    tokenizer = BertTokenizerFast.from_pretrained(args.bert_model)
    model = CasRelModel(args.bert_model, len(rel2id)).to(device)

    if args.train:
        train_dataset = CasRelDataset(train_data, tokenizer, rel2id)
        # Set num_workers=0 if debugging or on some Windows setups, otherwise use a higher number for speed
        train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=0) 
        print('Starting training...')
        train_model(model, train_loader, dev_data, tokenizer, id2rel, device, args.epochs)
    
    if args.test:
        model_path = 'best_casrel_model.pt'
        if os.path.exists(model_path):
            model.load_state_dict(torch.load(model_path, map_location=device))
            print(f'Loaded best model from {model_path} for final evaluation.')
        else:
            print('No trained model found. Run with --train first.')
            if not args.train: return

        print("\n--- Final Evaluation on Test Set ---")
        precision, recall, f1 = evaluate_model(model, test_data, tokenizer, id2rel, device)
        print(f'Test Set Results: Precision={precision:.4f}, Recall={recall:.4f}, F1-score={f1:.4f}')

if __name__ == '__main__':
    main()