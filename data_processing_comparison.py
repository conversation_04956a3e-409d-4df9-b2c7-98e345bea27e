#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比Torch版本和Keras版本在实际数据处理中的差异
重点分析为什么Torch版本收敛更慢
"""

def analyze_data_processing_differences():
    """分析数据处理差异对训练的影响"""
    
    print("=" * 80)
    print("🔍 Torch版本 vs Keras版本数据处理差异分析")
    print("=" * 80)
    
    # 模拟实际训练数据统计
    print("\n📊 模拟训练数据统计:")
    
    # 假设的数据集统计
    total_samples = 56196  # NYT训练集大小
    
    # Keras版本：使用简单字符串匹配
    keras_stats = {
        "total_triples": 89000,
        "successful_mappings": 85000,  # 95.5% 成功率
        "failed_mappings": 4000,
        "effective_samples": 54000,   # 96% 样本有效
        "avg_entities_per_sample": 1.52
    }
    
    # Torch版本：使用精确offset mapping
    torch_stats = {
        "total_triples": 89000,
        "successful_mappings": 76000,  # 85.4% 成功率（更严格）
        "failed_mappings": 13000,
        "effective_samples": 48000,   # 85% 样本有效
        "avg_entities_per_sample": 1.35
    }
    
    print(f"\n🔧 Keras版本统计:")
    print(f"  总三元组数: {keras_stats['total_triples']:,}")
    print(f"  成功映射: {keras_stats['successful_mappings']:,} ({keras_stats['successful_mappings']/keras_stats['total_triples']*100:.1f}%)")
    print(f"  失败映射: {keras_stats['failed_mappings']:,}")
    print(f"  有效样本: {keras_stats['effective_samples']:,} / {total_samples:,} ({keras_stats['effective_samples']/total_samples*100:.1f}%)")
    print(f"  平均实体数/样本: {keras_stats['avg_entities_per_sample']:.2f}")
    
    print(f"\n🔧 Torch版本统计:")
    print(f"  总三元组数: {torch_stats['total_triples']:,}")
    print(f"  成功映射: {torch_stats['successful_mappings']:,} ({torch_stats['successful_mappings']/torch_stats['total_triples']*100:.1f}%)")
    print(f"  失败映射: {torch_stats['failed_mappings']:,}")
    print(f"  有效样本: {torch_stats['effective_samples']:,} / {total_samples:,} ({torch_stats['effective_samples']/total_samples*100:.1f}%)")
    print(f"  平均实体数/样本: {torch_stats['avg_entities_per_sample']:.2f}")
    
    # 计算差异影响
    sample_loss = keras_stats['effective_samples'] - torch_stats['effective_samples']
    triple_loss = keras_stats['successful_mappings'] - torch_stats['successful_mappings']
    
    print(f"\n📉 差异影响:")
    print(f"  样本损失: {sample_loss:,} ({sample_loss/keras_stats['effective_samples']*100:.1f}%)")
    print(f"  三元组损失: {triple_loss:,} ({triple_loss/keras_stats['successful_mappings']*100:.1f}%)")
    
    print(f"\n🎯 具体问题分析:")
    
    problem_cases = [
        {
            "问题": "标点符号处理",
            "示例": "Apple Inc. → ['Apple', 'Inc', '.'] vs ['Apple', 'Inc.']",
            "Keras影响": "宽松匹配，忽略标点差异",
            "Torch影响": "严格匹配，标点不一致导致失败",
            "失败率": "~15%"
        },
        {
            "问题": "子词分割边界",
            "示例": "COVID-19 → ['COVID', '-', '19'] vs ['COVI', '##D-19']",
            "Keras影响": "基于空格分割，相对稳定",
            "Torch影响": "WordPiece分割，边界敏感",
            "失败率": "~8%"
        },
        {
            "问题": "大小写敏感性",
            "示例": "U.S. vs u.s.",
            "Keras影响": "可能进行大小写归一化",
            "Torch影响": "保持原始大小写，更严格",
            "失败率": "~5%"
        },
        {
            "问题": "特殊字符处理",
            "示例": "实体包含引号、括号等",
            "Keras影响": "简单字符串匹配，较宽松",
            "Torch影响": "精确字符位置匹配",
            "失败率": "~12%"
        }
    ]
    
    for i, case in enumerate(problem_cases, 1):
        print(f"\n  {i}. **{case['问题']}**")
        print(f"     示例: {case['示例']}")
        print(f"     Keras: {case['Keras影响']}")
        print(f"     Torch: {case['Torch影响']}")
        print(f"     估计失败率: {case['失败率']}")
    
    print(f"\n🚀 收敛速度影响分析:")
    
    # 计算训练效率影响
    keras_efficiency = keras_stats['effective_samples'] * keras_stats['avg_entities_per_sample']
    torch_efficiency = torch_stats['effective_samples'] * torch_stats['avg_entities_per_sample']
    efficiency_ratio = torch_efficiency / keras_efficiency
    
    print(f"\n📈 训练效率对比:")
    print(f"  Keras有效训练信号: {keras_efficiency:,.0f}")
    print(f"  Torch有效训练信号: {torch_efficiency:,.0f}")
    print(f"  效率比率: {efficiency_ratio:.3f} (Torch相对于Keras)")
    print(f"  预期收敛速度: {1/efficiency_ratio:.1f}x 慢于Keras版本")
    
    print(f"\n🔧 具体影响机制:")
    
    mechanisms = [
        "1. **训练样本减少**: 更少的有效样本意味着每个epoch的学习信号减少",
        "2. **标签质量vs数量权衡**: 更精确的标签但数量减少，可能需要更多epoch收敛",
        "3. **梯度稀疏性**: 更少的正样本导致梯度更稀疏，更新更慢",
        "4. **类别不平衡加剧**: 有效正样本减少，负样本比例增加",
        "5. **学习难度增加**: 更精确的边界要求模型学习更细粒度的特征"
    ]
    
    for mechanism in mechanisms:
        print(f"  {mechanism}")
    
    print(f"\n💡 改进建议:")
    
    suggestions = [
        "1. **数据预处理优化**: 改进实体匹配策略，提高映射成功率",
        "2. **损失函数调整**: 使用focal loss处理类别不平衡",
        "3. **学习率策略**: 使用更小的初始学习率和warmup",
        "4. **数据增强**: 通过同义词替换等方式增加训练样本",
        "5. **混合策略**: 结合精确匹配和模糊匹配的优点"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

if __name__ == "__main__":
    analyze_data_processing_differences()
