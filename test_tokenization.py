#!/usr/bin/env python3
"""
测试BertTokenizer实现的正确性
"""

from transformers import BertTokenizer
import torch

def test_tokenization():
    """测试tokenization的一致性"""
    tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
    
    # 测试样例
    text = "<PERSON> was born in Hawaii."
    entity1 = "Barack Obama"
    entity2 = "Hawaii"
    
    print("=== Tokenization测试 ===")
    print(f"原文: {text}")
    print(f"实体1: {entity1}")
    print(f"实体2: {entity2}")
    print()
    
    # 方法1: 直接encode (包含[CLS][SEP])
    encoding = tokenizer(text, return_tensors='pt')
    tokens_with_special = tokenizer.convert_ids_to_tokens(encoding['input_ids'][0])
    print(f"完整tokens (含特殊符号): {tokens_with_special}")
    
    # 方法2: 只tokenize (不含[CLS][SEP])
    tokens_only = tokenizer.tokenize(text)
    print(f"纯tokens (不含特殊符号): {tokens_only}")
    print()
    
    # 测试实体tokenization
    entity1_tokens = tokenizer.tokenize(entity1)
    entity2_tokens = tokenizer.tokenize(entity2)
    print(f"实体1 tokens: {entity1_tokens}")
    print(f"实体2 tokens: {entity2_tokens}")
    print()
    
    # 测试子序列查找
    def find_sublist(main_list, sub_list):
        if not sub_list:
            return -1
        for i in range(len(main_list) - len(sub_list) + 1):
            if main_list[i:i+len(sub_list)] == sub_list:
                return i
        return -1
    
    # 在完整tokens中查找实体
    pos1_with_special = find_sublist(tokens_with_special, entity1_tokens)
    pos2_with_special = find_sublist(tokens_with_special, entity2_tokens)
    
    # 在纯tokens中查找实体
    pos1_only = find_sublist(tokens_only, entity1_tokens)
    pos2_only = find_sublist(tokens_only, entity2_tokens)
    
    print("=== 位置查找测试 ===")
    print(f"在完整tokens中找到 '{entity1}': 位置 {pos1_with_special}")
    print(f"在完整tokens中找到 '{entity2}': 位置 {pos2_with_special}")
    print(f"在纯tokens中找到 '{entity1}': 位置 {pos1_only}")
    print(f"在纯tokens中找到 '{entity2}': 位置 {pos2_only}")
    print()
    
    # 验证提取的正确性
    if pos1_with_special != -1:
        extracted1 = tokens_with_special[pos1_with_special:pos1_with_special+len(entity1_tokens)]
        print(f"从完整tokens提取的实体1: {extracted1}")
        print(f"是否匹配: {extracted1 == entity1_tokens}")
    
    if pos2_with_special != -1:
        extracted2 = tokens_with_special[pos2_with_special:pos2_with_special+len(entity2_tokens)]
        print(f"从完整tokens提取的实体2: {extracted2}")
        print(f"是否匹配: {extracted2 == entity2_tokens}")
    
    print()
    print("=== 结论 ===")
    print("1. BertTokenizer.tokenize() 不会添加[CLS][SEP]")
    print("2. BertTokenizer() 调用会添加[CLS][SEP]")
    print("3. 我们的实现使用encoding方式，所以tokens包含[CLS][SEP]")
    print("4. 实体tokenization不包含[CLS][SEP]")
    print("5. 这种组合是正确的，不需要[1:-1]处理")

def test_casrel_data_processing():
    """测试CasRel数据处理流程"""
    from casrel_torch import CasRelDataset
    
    tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
    
    # 模拟数据
    sample_data = [{
        'text': 'Barack Obama was born in Hawaii.',
        'triple_list': [
            ('Barack Obama', 'born_in', 'Hawaii')
        ]
    }]
    
    rel2id = {'born_in': 0}
    
    print("\n=== CasRel数据处理测试 ===")
    
    dataset = CasRelDataset(sample_data, tokenizer, rel2id, max_len=32)
    item = dataset[0]
    
    print(f"Input IDs shape: {item['input_ids'].shape}")
    print(f"Attention mask shape: {item['attention_mask'].shape}")
    print(f"Sub heads shape: {item['sub_heads'].shape}")
    print(f"Sub tails shape: {item['sub_tails'].shape}")
    print(f"Obj heads shape: {item['obj_heads'].shape}")
    print(f"Obj tails shape: {item['obj_tails'].shape}")
    
    # 检查标签
    tokens = tokenizer.convert_ids_to_tokens(item['input_ids'])
    print(f"\nTokens: {tokens}")
    
    sub_head_positions = torch.where(item['sub_heads'] == 1)[0]
    sub_tail_positions = torch.where(item['sub_tails'] == 1)[0]
    
    print(f"Subject head positions: {sub_head_positions.tolist()}")
    print(f"Subject tail positions: {sub_tail_positions.tolist()}")
    
    if len(sub_head_positions) > 0 and len(sub_tail_positions) > 0:
        sub_start = sub_head_positions[0].item()
        sub_end = sub_tail_positions[0].item()
        subject_tokens = tokens[sub_start:sub_end+1]
        print(f"提取的subject tokens: {subject_tokens}")
        
        # 重构文本
        subject_text = tokenizer.convert_tokens_to_string(subject_tokens)
        print(f"重构的subject文本: '{subject_text}'")
    
    # 检查object标签
    obj_head_positions = torch.where(item['obj_heads'] > 0)
    obj_tail_positions = torch.where(item['obj_tails'] > 0)
    
    print(f"Object head positions: {obj_head_positions}")
    print(f"Object tail positions: {obj_tail_positions}")

if __name__ == "__main__":
    test_tokenization()
    test_casrel_data_processing()
