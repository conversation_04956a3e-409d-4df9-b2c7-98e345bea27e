#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比原始Torch版本和改进版本的样本构造效果
"""

import json
import torch
from torch.utils.data import DataLoader
from transformers import BertTokenizerFast
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

def load_sample_data():
    """加载少量样本数据进行测试"""
    sample_data = [
        {
            "text": "Apple Inc. was founded by <PERSON> in Cupertino, California.",
            "triple_list": [
                ["Apple Inc.", "founded_by", "<PERSON> Jobs"],
                ["Apple Inc.", "located_in", "Cupertino"],
                ["Steve Jobs", "born_in", "California"]
            ]
        },
        {
            "text": "The U.S. government announced new policies regarding COVID-19.",
            "triple_list": [
                ["U.S. government", "announced", "policies"],
                ["policies", "regarding", "COVID-19"]
            ]
        },
        {
            "text": "Microsoft Corporation is headquartered in Redmond, Washington.",
            "triple_list": [
                ["Microsoft Corporation", "headquartered_in", "Redmond"],
                ["Redmond", "located_in", "Washington"]
            ]
        },
        {
            "text": "This is a sentence with no valid entities or relations.",
            "triple_list": [
                ["NonExistentEntity", "fake_relation", "AnotherFakeEntity"]
            ]
        }
    ]
    
    # 创建简单的rel2id映射
    rel2id = {
        "founded_by": 0,
        "located_in": 1,
        "born_in": 2,
        "announced": 3,
        "regarding": 4,
        "headquartered_in": 5,
        "fake_relation": 6
    }
    
    return sample_data, rel2id

def test_original_dataset():
    """测试原始Torch版本的Dataset"""
    try:
        from casrel_torch import CasRelDataset
        
        print("🔧 测试原始Torch版本Dataset:")
        
        data, rel2id = load_sample_data()
        tokenizer = BertTokenizerFast.from_pretrained('bert-base-cased')
        
        dataset = CasRelDataset(data, tokenizer, rel2id, max_len=128)
        
        valid_samples = 0
        empty_samples = 0
        
        for i in range(len(dataset)):
            try:
                sample = dataset[i]
                # 检查是否有有效的subject
                if sample['sub_head_idx'].item() == 0 and sample['sub_tail_idx'].item() == 0:
                    # 检查是否真的没有有效标签
                    if sample['sub_heads'].sum().item() == 0:
                        empty_samples += 1
                    else:
                        valid_samples += 1
                else:
                    valid_samples += 1
                    
                print(f"  样本 {i+1}: 有效主体数={sample['sub_heads'].sum().item():.0f}, "
                      f"选中主体位置=({sample['sub_head_idx'].item()}, {sample['sub_tail_idx'].item()})")
                      
            except Exception as e:
                print(f"  样本 {i+1}: 处理失败 - {e}")
                empty_samples += 1
        
        print(f"  总结: 有效样本={valid_samples}, 空样本={empty_samples}")
        return valid_samples, empty_samples
        
    except ImportError as e:
        print(f"无法导入原始版本: {e}")
        return 0, 0

def test_improved_dataset():
    """测试改进版本的Dataset"""
    try:
        from casrel_torch_improved import CasRelDatasetImproved, collate_fn
        
        print("\n🚀 测试改进版本Dataset:")
        
        data, rel2id = load_sample_data()
        tokenizer = BertTokenizerFast.from_pretrained('bert-base-cased')
        
        dataset = CasRelDatasetImproved(data, tokenizer, rel2id, max_len=100)
        
        valid_samples = 0
        none_samples = 0
        
        samples = []
        for i in range(len(dataset)):
            try:
                sample = dataset[i]
                if sample is None:
                    none_samples += 1
                    print(f"  样本 {i+1}: None (无有效三元组)")
                else:
                    valid_samples += 1
                    samples.append(sample)
                    print(f"  样本 {i+1}: 有效主体数={sample['sub_heads'].sum().item():.0f}, "
                          f"选中主体位置=({sample['sub_head_idx'].item()}, {sample['sub_tail_idx'].item()})")
                          
            except Exception as e:
                print(f"  样本 {i+1}: 处理失败 - {e}")
                none_samples += 1
        
        print(f"  总结: 有效样本={valid_samples}, None样本={none_samples}")
        
        # 测试DataLoader
        if samples:
            print(f"\n  测试DataLoader (batch_size=2):")
            try:
                loader = DataLoader(dataset, batch_size=2, shuffle=False, collate_fn=collate_fn)
                batch_count = 0
                total_samples_in_batches = 0
                
                for batch in loader:
                    if batch is not None:
                        batch_count += 1
                        batch_size = batch['input_ids'].size(0)
                        total_samples_in_batches += batch_size
                        print(f"    Batch {batch_count}: {batch_size} 样本")
                    else:
                        print(f"    跳过空batch")
                
                print(f"    总计: {batch_count} 个有效batch, {total_samples_in_batches} 个样本")
                
            except Exception as e:
                print(f"    DataLoader测试失败: {e}")
        
        return valid_samples, none_samples
        
    except ImportError as e:
        print(f"无法导入改进版本: {e}")
        return 0, 0

def compare_tokenization_strategies():
    """对比两种tokenization策略"""
    print("\n" + "="*60)
    print("🔍 Tokenization策略对比")
    print("="*60)
    
    data, rel2id = load_sample_data()
    tokenizer = BertTokenizerFast.from_pretrained('bert-base-cased')
    
    test_case = data[0]  # "Apple Inc. was founded by Steve Jobs..."
    text = test_case["text"]
    entities = ["Apple Inc.", "Steve Jobs"]
    
    print(f"测试文本: {text}")
    print(f"测试实体: {entities}")
    
    # 原始方法：offset mapping
    print(f"\n🔧 原始方法 (offset mapping):")
    encoding = tokenizer(text, return_offsets_mapping=True, add_special_tokens=True)
    tokens = tokenizer.tokenize(text)
    offset_mapping = encoding['offset_mapping']
    
    print(f"Tokens: {tokens}")
    print(f"Offset mapping: {offset_mapping}")
    
    for entity in entities:
        char_start = text.find(entity)
        if char_start != -1:
            char_end = char_start + len(entity)
            print(f"  实体 '{entity}': 字符位置 [{char_start}, {char_end}]")
            
            # 简化的offset mapping查找
            token_start, token_end = None, None
            for idx, (start, end) in enumerate(offset_mapping):
                if start == 0 and end == 0:
                    continue
                if token_start is None and start <= char_start < end:
                    token_start = idx
                if start < char_end <= end:
                    token_end = idx
            
            if token_start is not None and token_end is not None:
                print(f"    -> Token位置 [{token_start}, {token_end}]: {tokens[token_start:token_end+1]}")
            else:
                print(f"    -> 映射失败")
    
    # 改进方法：token匹配
    print(f"\n🚀 改进方法 (token匹配):")
    full_tokens = tokenizer.tokenize(text)
    print(f"Full tokens: {full_tokens}")
    
    for entity in entities:
        entity_tokens = tokenizer.tokenize(entity)
        # 去除[CLS][SEP]
        if entity_tokens and entity_tokens[0] == '[CLS]':
            entity_tokens = entity_tokens[1:]
        if entity_tokens and entity_tokens[-1] == '[SEP]':
            entity_tokens = entity_tokens[:-1]
            
        print(f"  实体 '{entity}' tokens: {entity_tokens}")
        
        # 查找token序列
        def find_head_idx(source, target):
            target_len = len(target)
            for i in range(len(source)):
                if source[i: i + target_len] == target:
                    return i
            return -1
        
        token_start = find_head_idx(full_tokens, entity_tokens)
        if token_start != -1:
            token_end = token_start + len(entity_tokens) - 1
            print(f"    -> Token位置 [{token_start}, {token_end}]: {full_tokens[token_start:token_end+1]}")
        else:
            print(f"    -> 匹配失败")

def main():
    print("="*60)
    print("🔍 CasRel Torch版本对比测试")
    print("="*60)
    
    # 测试原始版本
    orig_valid, orig_empty = test_original_dataset()
    
    # 测试改进版本
    imp_valid, imp_none = test_improved_dataset()
    
    # 对比tokenization策略
    compare_tokenization_strategies()
    
    # 总结
    print(f"\n" + "="*60)
    print("📊 对比总结")
    print("="*60)
    print(f"原始版本: {orig_valid} 有效样本, {orig_empty} 空标签样本")
    print(f"改进版本: {imp_valid} 有效样本, {imp_none} 过滤样本")
    print(f"\n关键改进:")
    print(f"1. 过滤策略: 原始版本包含空标签样本，改进版本完全过滤")
    print(f"2. 匹配策略: 改进版本使用更稳定的token匹配")
    print(f"3. 训练效率: 改进版本避免了无效样本的梯度计算")

if __name__ == "__main__":
    main()
