#!/usr/bin/env python3
"""
Test script to verify the evaluation methods are consistent between keras and torch versions
"""

def partial_match(pred_set, gold_set):
    """Apply partial match evaluation (only compare first word of entities)"""
    pred = {(i[0].split(' ')[0] if len(i[0].split(' ')) > 0 else i[0], i[1], i[2].split(' ')[0] if len(i[2].split(' ')) > 0 else i[2]) for i in pred_set}
    gold = {(i[0].split(' ')[0] if len(i[0].split(' ')) > 0 else i[0], i[1], i[2].split(' ')[0] if len(i[2].split(' ')) > 0 else i[2]) for i in gold_set}
    return pred, gold

def calculate_metrics(pred_triples, gold_triples, exact_match=False):
    """Calculate precision, recall, F1"""
    pred_set = set(pred_triples)
    gold_set = set(gold_triples)
    
    # Apply partial match if not exact match
    if not exact_match:
        pred_set, gold_set = partial_match(pred_set, gold_set)

    if len(pred_set) == 0:
        precision = 0
    else:
        precision = len(pred_set & gold_set) / len(pred_set)

    if len(gold_set) == 0:
        recall = 0
    else:
        recall = len(pred_set & gold_set) / len(gold_set)

    if precision + recall == 0:
        f1 = 0
    else:
        f1 = 2 * precision * recall / (precision + recall)

    return precision, recall, f1

def test_evaluation_methods():
    """Test both exact match and partial match evaluation"""
    
    # Test data with multi-word entities
    pred_triples = [
        ("Barack Obama", "born_in", "Hawaii"),
        ("Donald Trump", "president_of", "United States"),
        ("New York", "located_in", "USA")
    ]
    
    gold_triples = [
        ("Barack Hussein Obama", "born_in", "Hawaii"),  # Different subject
        ("Donald Trump", "president_of", "United States of America"),  # Different object
        ("New York City", "located_in", "USA")  # Different subject
    ]
    
    print("=== Test Data ===")
    print("Predicted triples:")
    for triple in pred_triples:
        print(f"  {triple}")
    print("\nGold triples:")
    for triple in gold_triples:
        print(f"  {triple}")
    
    print("\n=== Exact Match Evaluation ===")
    precision, recall, f1 = calculate_metrics(pred_triples, gold_triples, exact_match=True)
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    print(f"F1: {f1:.3f}")
    
    print("\n=== Partial Match Evaluation ===")
    precision, recall, f1 = calculate_metrics(pred_triples, gold_triples, exact_match=False)
    print(f"Precision: {precision:.3f}")
    print(f"Recall: {recall:.3f}")
    print(f"F1: {f1:.3f}")
    
    print("\n=== Partial Match Details ===")
    pred_set = set(pred_triples)
    gold_set = set(gold_triples)
    pred_partial, gold_partial = partial_match(pred_set, gold_set)
    
    print("Predicted (partial):")
    for triple in pred_partial:
        print(f"  {triple}")
    print("Gold (partial):")
    for triple in gold_partial:
        print(f"  {triple}")
    print("Matches:")
    for triple in pred_partial & gold_partial:
        print(f"  {triple}")

if __name__ == "__main__":
    test_evaluation_methods()
