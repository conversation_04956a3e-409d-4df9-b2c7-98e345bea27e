from transformers import BertTokenizer
import torch


def find_sublist( main_list, sub_list):
        """
        Find sublist in main list.
        NOTE: This is a simplified approach. A more robust way is to use character-level
        spans and the `offset_mapping` from the tokenizer to handle complex tokenization cases.
        """
        if not sub_list: return -1
        # Start search from index 1 to skip [CLS] token
        for i in range(1, len(main_list) - len(sub_list) + 1):
            if main_list[i:i+len(sub_list)] == sub_list:
                return i
        return -1

text = "<PERSON> was born in Hawaii."
entity1 = "Barack Obama"
entity2 = "Hawaii"

tokenizer = BertTokenizer.from_pretrained("bert-base-cased")
encoding = tokenizer(text, max_length=100, truncation=True,
                                padding='max_length', return_tensors='pt')

input_ids = encoding['input_ids'].squeeze(0) # Remove batch dim
attention_mask = encoding['attention_mask'].squeeze(0) # Remove batch dim
        
# We need the tokenized version of text to find entity spans
tokens = tokenizer.convert_ids_to_tokens(input_ids)

print(input_ids,attention_mask,tokens)
        
subj_tokens = tokenizer.tokenize(entity1)
obj_tokens = tokenizer.tokenize(entity2)
            
 # Find start indices
sub_start = find_sublist(tokens, subj_tokens)
obj_start = find_sublist(tokens, obj_tokens)

if sub_start != -1 and obj_start != -1:
        sub_end = sub_start + len(subj_tokens) - 1
        obj_end = obj_start + len(obj_tokens) - 1

print(sub_start,sub_end)
print(obj_start,obj_end)

print(tokenizer.decode(input_ids[sub_start:sub_end+1], skip_special_tokens=True))
print(tokenizer.decode(input_ids[obj_start:obj_end+1], skip_special_tokens=True))