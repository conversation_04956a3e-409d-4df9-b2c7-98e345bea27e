#! -*- coding:utf-8 -*-
"""
CasRel PyTorch Implementation - Improved Version
修复样本构造差异，学习Keras版本的动态过滤策略
"""
import torch
import torch.nn as nn
import json
import numpy as np
from transformers import BertModel, BertTokenizerFast
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import argparse
import os
import random
import re

class CasRelModel(nn.Module):
    """CasRel Model - 与原版相同"""
    def __init__(self, bert_model, num_rels):
        super().__init__()
        self.bert = BertModel.from_pretrained(bert_model)
        hidden_size = self.bert.config.hidden_size
        self.sub_heads = nn.Linear(hidden_size, 1)
        self.sub_tails = nn.Linear(hidden_size, 1)
        self.obj_heads = nn.Linear(hidden_size, num_rels)
        self.obj_tails = nn.Linear(hidden_size, num_rels)
        
    def forward(self, input_ids, attention_mask, sub_head_idx=None, sub_tail_idx=None):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        sequence_output = outputs.last_hidden_state
        sub_heads_logits = self.sub_heads(sequence_output).squeeze(-1)
        sub_tails_logits = self.sub_tails(sequence_output).squeeze(-1)
        sub_heads_pred = torch.sigmoid(sub_heads_logits)
        sub_tails_pred = torch.sigmoid(sub_tails_logits)
        
        if sub_head_idx is not None and sub_tail_idx is not None:
            sub_head_features = torch.gather(sequence_output, 1, sub_head_idx.unsqueeze(-1).expand(-1, -1, sequence_output.size(-1)))
            sub_tail_features = torch.gather(sequence_output, 1, sub_tail_idx.unsqueeze(-1).expand(-1, -1, sequence_output.size(-1)))
            sub_features = (sub_head_features + sub_tail_features) / 2
            enhanced_output = sequence_output + sub_features
            obj_heads_logits = self.obj_heads(enhanced_output)
            obj_tails_logits = self.obj_tails(enhanced_output)
            obj_heads_pred = torch.sigmoid(obj_heads_logits)
            obj_tails_pred = torch.sigmoid(obj_tails_logits)
            return sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred
        return sub_heads_pred, sub_tails_pred

def find_head_idx(source, target):
    """Keras版本的实体匹配函数"""
    target_len = len(target)
    for i in range(len(source)):
        if source[i: i + target_len] == target:
            return i
    return -1

class CasRelDatasetImproved(Dataset):
    """
    改进的Dataset类，学习Keras版本的动态过滤策略
    """
    def __init__(self, data, tokenizer, rel2id, max_len=128):
        self.data = data
        self.tokenizer = tokenizer
        self.rel2id = rel2id
        self.max_len = max_len
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        text = item['text']
        
        # 学习Keras版本：先截断文本到max_len个词
        text = ' '.join(text.split()[:self.max_len])
        
        # 使用标准tokenization（不需要offset_mapping）
        tokens = self.tokenizer.tokenize(text)
        if len(tokens) > 512:  # BERT_MAX_LEN
            tokens = tokens[:512]
        text_len = len(tokens)
        
        # 学习Keras版本：构建s2ro_map
        s2ro_map = {}
        for triple in item['triple_list']:
            subj, rel, obj = triple
            
            # 学习Keras版本：对实体单独tokenize并去除[CLS][SEP]
            subj_tokens = self.tokenizer.tokenize(subj)
            if subj_tokens and subj_tokens[0] == '[CLS]':
                subj_tokens = subj_tokens[1:]
            if subj_tokens and subj_tokens[-1] == '[SEP]':
                subj_tokens = subj_tokens[:-1]
                
            obj_tokens = self.tokenizer.tokenize(obj)
            if obj_tokens and obj_tokens[0] == '[CLS]':
                obj_tokens = obj_tokens[1:]
            if obj_tokens and obj_tokens[-1] == '[SEP]':
                obj_tokens = obj_tokens[:-1]
            
            # 在完整文本的tokens中查找实体tokens
            sub_head_idx = find_head_idx(tokens, subj_tokens)
            obj_head_idx = find_head_idx(tokens, obj_tokens)
            
            if sub_head_idx != -1 and obj_head_idx != -1:
                sub = (sub_head_idx, sub_head_idx + len(subj_tokens) - 1)
                if sub not in s2ro_map:
                    s2ro_map[sub] = []
                s2ro_map[sub].append((obj_head_idx,
                                   obj_head_idx + len(obj_tokens) - 1,
                                   self.rel2id[rel]))
        
        # 学习Keras版本：如果没有有效的s2ro_map，返回None
        if not s2ro_map:
            return None
        
        # 构造输入
        encoding = self.tokenizer(
            text, 
            max_length=self.max_len, 
            truncation=True,
            padding='max_length', 
            return_tensors='pt'
        )
        
        input_ids = encoding['input_ids'].squeeze(0)
        attention_mask = encoding['attention_mask'].squeeze(0)
        
        # 初始化标签
        seq_len = input_ids.size(0)
        sub_heads = torch.zeros(seq_len)
        sub_tails = torch.zeros(seq_len)
        obj_heads = torch.zeros(seq_len, len(self.rel2id))
        obj_tails = torch.zeros(seq_len, len(self.rel2id))
        
        # 设置subject标签
        for s in s2ro_map:
            if s[0] < seq_len and s[1] < seq_len:  # 确保索引有效
                sub_heads[s[0]] = 1
                sub_tails[s[1]] = 1
        
        # 随机选择一个subject用于object预测
        sub_head, sub_tail = random.choice(list(s2ro_map.keys()))
        sub_head_idx = torch.tensor([sub_head])
        sub_tail_idx = torch.tensor([sub_tail])
        
        # 设置object标签
        for ro in s2ro_map.get((sub_head, sub_tail), []):
            if ro[0] < seq_len and ro[1] < seq_len:  # 确保索引有效
                obj_heads[ro[0], ro[2]] = 1
                obj_tails[ro[1], ro[2]] = 1
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'sub_heads': sub_heads,
            'sub_tails': sub_tails,
            'obj_heads': obj_heads,
            'obj_tails': obj_tails,
            'sub_head_idx': sub_head_idx,
            'sub_tail_idx': sub_tail_idx
        }

def collate_fn(batch):
    """自定义collate函数，过滤None样本"""
    # 过滤掉None样本
    batch = [item for item in batch if item is not None]
    
    if len(batch) == 0:
        return None
    
    # 正常的batch构造
    keys = batch[0].keys()
    batched = {}
    
    for key in keys:
        batched[key] = torch.stack([item[key] for item in batch])
    
    return batched

def load_data_simple(data_path, rel_path):
    """简化的数据加载，不进行预处理"""
    # Load relation data
    with open(rel_path, 'r', encoding='utf-8') as f:
        rel_data = json.load(f)
    id2rel = {int(k): v for k, v in rel_data[0].items()}
    rel2id = rel_data[1]

    # Load raw data without preprocessing
    with open(data_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"Loaded {len(data)} samples from {data_path}")
    return data, id2rel, rel2id

def train_model_improved(model, train_loader, dev_data, tokenizer, id2rel, device, epochs=10):
    """改进的训练函数"""
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-5)
    criterion = nn.BCELoss(reduction='none')
    best_f1 = 0
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        valid_batches = 0
        
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{epochs}')
        for batch in progress_bar:
            # 跳过空batch
            if batch is None:
                continue
                
            valid_batches += 1
            optimizer.zero_grad()
            
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            sub_heads_gold = batch['sub_heads'].to(device)
            sub_tails_gold = batch['sub_tails'].to(device)
            obj_heads_gold = batch['obj_heads'].to(device)
            obj_tails_gold = batch['obj_tails'].to(device)
            sub_head_idx = batch['sub_head_idx'].to(device)
            sub_tail_idx = batch['sub_tail_idx'].to(device)
            
            sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred = model(
                input_ids, attention_mask, sub_head_idx, sub_tail_idx)
            
            mask = attention_mask.float()
            
            # Loss calculation
            sub_heads_loss = (criterion(sub_heads_pred, sub_heads_gold) * mask).sum() / mask.sum()
            sub_tails_loss = (criterion(sub_tails_pred, sub_tails_gold) * mask).sum() / mask.sum()
            obj_mask = mask.unsqueeze(-1).expand_as(obj_heads_gold)
            obj_heads_loss = (criterion(obj_heads_pred, obj_heads_gold) * obj_mask).sum() / obj_mask.sum()
            obj_tails_loss = (criterion(obj_tails_pred, obj_tails_gold) * obj_mask).sum() / obj_mask.sum()
            loss = sub_heads_loss + sub_tails_loss + obj_heads_loss + obj_tails_loss
            
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
            progress_bar.set_postfix({'loss': f'{loss.item():.4f}', 'valid_batches': valid_batches})

        avg_loss = total_loss / valid_batches if valid_batches > 0 else 0
        print(f'\nEpoch {epoch+1} - Average Loss: {avg_loss:.4f}, Valid Batches: {valid_batches}')
        
        # 这里可以添加评估逻辑
        print(f'Epoch {epoch+1} completed')

def main():
    parser = argparse.ArgumentParser(description='CasRel PyTorch - Improved Version')
    parser.add_argument('--dataset', default='NYT', help='Dataset name')
    parser.add_argument('--batch_size', default=6, type=int, help='Batch size (学习Keras版本)')
    parser.add_argument('--epochs', default=20, type=int, help='Number of epochs')
    parser.add_argument('--bert_model', default='bert-base-cased', type=str, help='BERT model')
    parser.add_argument('--train', action='store_true', help='Train the model')
    args = parser.parse_args()

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f'Using device: {device}')

    dataset_path = f'data/{args.dataset}'
    rel_path = os.path.join(dataset_path, 'rel2id.json')
    
    # 使用简化的数据加载
    train_data, id2rel, rel2id = load_data_simple(os.path.join(dataset_path, 'train_triples.json'), rel_path)
    dev_data, _, _ = load_data_simple(os.path.join(dataset_path, 'dev_triples.json'), rel_path)

    print(f'Number of relations: {len(rel2id)}')

    tokenizer = BertTokenizerFast.from_pretrained(args.bert_model)
    model = CasRelModel(args.bert_model, len(rel2id)).to(device)

    if args.train:
        train_dataset = CasRelDatasetImproved(train_data, tokenizer, rel2id, max_len=100)  # 学习Keras的max_len
        train_loader = DataLoader(
            train_dataset, 
            batch_size=args.batch_size, 
            shuffle=True, 
            num_workers=0,
            collate_fn=collate_fn  # 使用自定义collate函数
        )
        print('Starting improved training...')
        train_model_improved(model, train_loader, dev_data, tokenizer, id2rel, device, args.epochs)

if __name__ == '__main__':
    main()
