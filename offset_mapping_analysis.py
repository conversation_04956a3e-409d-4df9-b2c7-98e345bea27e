#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Torch版本offset mapping与Keras版本简单匹配的差异
演示为什么offset mapping可能导致训练数据标签噪声增加
"""

def simulate_bert_tokenization(text):
    """模拟BERT tokenization和offset mapping"""
    tokens = []
    offset_mapping = []

    # 添加[CLS]
    tokens.append('[CLS]')
    offset_mapping.append((0, 0))  # 特殊token

    i = 0
    while i < len(text):
        if text[i].isspace():
            i += 1
            continue

        # 找到单词边界
        word_start = i
        while i < len(text) and not text[i].isspace():
            i += 1
        word = text[word_start:i]

        # 模拟WordPiece分词
        if len(word) <= 4:
            tokens.append(word)
            offset_mapping.append((word_start, i))
        else:
            # 长单词分割成子词
            mid = len(word) // 2
            tokens.append(word[:mid])
            offset_mapping.append((word_start, word_start + mid))
            tokens.append('##' + word[mid:])
            offset_mapping.append((word_start + mid, i))

    # 添加[SEP]
    tokens.append('[SEP]')
    offset_mapping.append((0, 0))  # 特殊token

    return tokens, offset_mapping

def simulate_keras_tokenization(text):
    """模拟Keras版本的tokenization"""
    tokens = []
    words = text.split()

    for word in words:
        if len(word) <= 4:
            tokens.append(word)
        else:
            # 简单分割
            mid = len(word) // 2
            tokens.append(word[:mid])
            tokens.append('##' + word[mid:])
        tokens.append('[unused1]')  # Keras版本的特殊标记

    return tokens

def find_head_idx(source, target):
    """Keras版本使用的简单匹配函数"""
    target_len = len(target)
    for i in range(len(source)):
        if source[i: i + target_len] == target:
            return i
    return -1

def char_span_to_token_span_torch(offset_mapping, char_span):
    """Torch版本的精确映射函数"""
    char_start, char_end = char_span
    token_start_index = -1
    token_end_index = -1

    for idx, (start, end) in enumerate(offset_mapping):
        if start == 0 and end == 0:
            continue
        
        if token_start_index == -1 and start <= char_start < end:
            token_start_index = idx
        
        if start < char_end <= end:
            token_end_index = idx

    if token_start_index == -1 or token_end_index == -1 or token_start_index > token_end_index:
        return None
        
    return (token_start_index, token_end_index)

def analyze_tokenization_differences():
    """分析两种方法的差异"""

    # 测试用例：包含复杂实体的句子
    test_cases = [
        {
            "text": "Apple Inc. was founded by Steve Jobs in Cupertino, California.",
            "entities": [
                ("Apple Inc.", [0, 10]),  # 包含标点符号
                ("Steve Jobs", [27, 37]),  # 简单实体
                ("Cupertino, California", [41, 64])  # 包含逗号和空格
            ]
        },
        {
            "text": "The U.S. government announced new policies.",
            "entities": [
                ("U.S. government", [4, 20]),  # 包含缩写和标点
            ]
        },
        {
            "text": "COVID-19 pandemic affected the world economy.",
            "entities": [
                ("COVID-19", [0, 8]),  # 包含连字符
                ("world economy", [31, 45])
            ]
        }
    ]

    print("=" * 80)
    print("Torch版本offset mapping vs Keras版本简单匹配的差异分析")
    print("=" * 80)

    total_torch_success = 0
    total_keras_success = 0
    total_entities = 0

    for case_idx, case in enumerate(test_cases):
        text = case["text"]
        entities = case["entities"]

        print(f"\n📝 测试案例 {case_idx + 1}:")
        print(f"文本: {text}")

        # 模拟Torch版本处理
        torch_tokens, offset_mapping = simulate_bert_tokenization(text)

        print(f"\n🔧 Torch版本tokenization:")
        print(f"Tokens: {torch_tokens}")
        print(f"Offset mapping: {offset_mapping}")

        # 模拟Keras版本处理
        keras_tokens = simulate_keras_tokenization(text)

        print(f"\n🔧 Keras版本tokenization:")
        print(f"Tokens: {keras_tokens}")

        print(f"\n🎯 实体映射结果:")

        for entity_text, char_span in entities:
            total_entities += 1
            print(f"\n  实体: '{entity_text}' 字符位置: {char_span}")

            # Torch版本映射
            torch_result = char_span_to_token_span_torch(offset_mapping, char_span)
            if torch_result:
                torch_start, torch_end = torch_result
                if torch_start < len(torch_tokens) and torch_end < len(torch_tokens):
                    mapped_tokens = torch_tokens[torch_start:torch_end+1]
                    print(f"    ✅ Torch映射成功: token位置 [{torch_start}, {torch_end}] -> {mapped_tokens}")
                    total_torch_success += 1
                else:
                    print(f"    ❌ Torch映射失败: token索引超出范围")
            else:
                print(f"    ❌ Torch映射失败: 无法找到对应的token位置")

            # Keras版本映射（模拟简单匹配）
            # 简化实体文本处理
            entity_words = entity_text.replace(',', '').replace('.', '').split()
            keras_result = find_head_idx(keras_tokens, entity_words)
            if keras_result != -1:
                keras_end = keras_result + len(entity_words) - 1
                if keras_end < len(keras_tokens):
                    mapped_tokens = keras_tokens[keras_result:keras_end+1]
                    print(f"    ✅ Keras映射成功: token位置 [{keras_result}, {keras_end}] -> {mapped_tokens}")
                    total_keras_success += 1
                else:
                    print(f"    ❌ Keras映射失败: token索引超出范围")
            else:
                print(f"    ❌ Keras映射失败: 无法找到对应的token序列")

    print(f"\n" + "=" * 80)
    print(f"📊 总体统计:")
    print(f"总实体数: {total_entities}")
    print(f"Torch版本成功率: {total_torch_success}/{total_entities} ({total_torch_success/total_entities*100:.1f}%)")
    print(f"Keras版本成功率: {total_keras_success}/{total_entities} ({total_keras_success/total_entities*100:.1f}%)")

    print(f"\n🔍 关键差异分析:")
    print(f"1. **精确度差异**:")
    print(f"   - Torch版本: 使用字符级精确映射，对标点、空格、特殊字符敏感")
    print(f"   - Keras版本: 使用token序列匹配，相对宽松")

    print(f"\n2. **训练数据影响**:")
    print(f"   - Torch版本: 更严格的边界检测可能导致更多实体被过滤掉")
    print(f"   - Keras版本: 更宽松的匹配可能包含更多训练样本")

    print(f"\n3. **收敛速度影响**:")
    print(f"   - 更少的有效训练样本 → 更慢的收敛")
    print(f"   - 更精确的标签 → 更难学习的边界")

if __name__ == "__main__":
    analyze_tokenization_differences()
