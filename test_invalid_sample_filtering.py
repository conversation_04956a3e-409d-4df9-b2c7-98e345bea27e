#!/usr/bin/env python3
"""
Test script to verify that invalid sample filtering works correctly
"""

import torch
from transformers import BertTokenizer
import json

# Import the modified CasRelDataset
from casrel_torch import CasRelDataset

def create_test_data():
    """Create test data with both valid and invalid samples"""
    return [
        {
            "text": "<PERSON> works at Google in California.",
            "triple_list": [
                ["<PERSON>", "works_at", "Google"],
                ["Google", "located_in", "California"]
            ]
        },
        {
            "text": "This is a sentence with no entities that can be found.",
            "triple_list": [
                ["NonExistentEntity", "relation", "AnotherNonExistentEntity"]
            ]
        },
        {
            "text": "Apple is a technology company founded by <PERSON>.",
            "triple_list": [
                ["Apple", "founded_by", "Steve <PERSON>s"],
                ["Apple", "is_a", "technology company"]
            ]
        },
        {
            "text": "Random text without any matching entities.",
            "triple_list": [
                ["XYZ", "unknown_relation", "ABC"]
            ]
        },
        {
            "text": "Microsoft was founded by <PERSON>",
            "triple_list": [
                ["Microsoft", "founded_by", "<PERSON>"]
            ]
        }
    ]

def create_test_rel2id():
    """Create test relation to ID mapping"""
    return {
        "works_at": 0,
        "located_in": 1,
        "founded_by": 2,
        "is_a": 3,
        "unknown_relation": 4
    }

def test_filtering():
    """Test the invalid sample filtering functionality"""
    print("Testing invalid sample filtering...")
    
    # Create test data
    test_data = create_test_data()
    rel2id = create_test_rel2id()
    
    print(f"Original dataset size: {len(test_data)}")
    
    # Initialize tokenizer
    tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
    
    # Create dataset with filtering
    dataset = CasRelDataset(test_data, tokenizer, rel2id, max_len=128)
    
    print(f"Filtered dataset size: {len(dataset)}")
    print(f"Filtering ratio: {len(dataset)}/{len(test_data)} = {len(dataset)/len(test_data):.2%}")
    
    # Test that we can access all samples without errors
    print("\nTesting sample access:")
    for i in range(len(dataset)):
        try:
            sample = dataset[i]
            print(f"Sample {i}: Successfully loaded")
            print(f"  - Input shape: {sample['input_ids'].shape}")
            print(f"  - Has valid subjects: {sample['sub_heads'].sum().item() > 0}")
            print(f"  - Has valid objects: {sample['obj_heads'].sum().item() > 0}")
        except Exception as e:
            print(f"Sample {i}: Error - {e}")
    
    print("\nTest completed successfully!")

if __name__ == "__main__":
    test_filtering()
