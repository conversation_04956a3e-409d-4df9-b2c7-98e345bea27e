#!/usr/bin/env python3
"""
测试完整的CasRel pipeline
"""

import torch
from transformers import BertTokenizer
from casrel_torch import CasRelModel, CasRelDataset, extract_triples

def test_full_pipeline():
    """测试完整的训练和预测流程"""
    
    # 初始化
    tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 模拟数据
    sample_data = [
        {
            'text': '<PERSON> was born in Hawaii.',
            'triple_list': [('<PERSON>', 'born_in', 'Hawaii')]
        },
        {
            'text': 'Apple CEO <PERSON> announced new products.',
            'triple_list': [('<PERSON>', 'CEO_of', 'Apple')]
        }
    ]
    
    rel2id = {'born_in': 0, 'CEO_of': 1}
    id2rel = {0: 'born_in', 1: 'CEO_of'}
    
    print("=== 完整Pipeline测试 ===")
    print(f"设备: {device}")
    print(f"关系数量: {len(rel2id)}")
    print()
    
    # 创建数据集
    dataset = CasRelDataset(sample_data, tokenizer, rel2id, max_len=32)
    print(f"数据集大小: {len(dataset)}")
    
    # 测试数据加载
    print("\n=== 数据加载测试 ===")
    for i, item in enumerate(dataset):
        print(f"样本 {i}:")
        tokens = tokenizer.convert_ids_to_tokens(item['input_ids'])
        print(f"  Tokens: {tokens[:10]}...")  # 只显示前10个
        
        # 检查subject标签
        sub_heads = torch.where(item['sub_heads'] == 1)[0]
        sub_tails = torch.where(item['sub_tails'] == 1)[0]
        print(f"  Subject heads: {sub_heads.tolist()}")
        print(f"  Subject tails: {sub_tails.tolist()}")
        
        # 检查object标签
        obj_heads = torch.where(item['obj_heads'] > 0)
        obj_tails = torch.where(item['obj_tails'] > 0)
        print(f"  Object heads: {obj_heads}")
        print(f"  Object tails: {obj_tails}")
        print()
    
    # 创建模型
    model = CasRelModel('bert-base-cased', len(rel2id)).to(device)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 测试前向传播
    print("\n=== 前向传播测试 ===")
    model.eval()
    with torch.no_grad():
        item = dataset[0]
        input_ids = item['input_ids'].unsqueeze(0).to(device)
        attention_mask = item['attention_mask'].unsqueeze(0).to(device)
        sub_head_idx = item['sub_head_idx'].unsqueeze(0).to(device)
        sub_tail_idx = item['sub_tail_idx'].unsqueeze(0).to(device)
        
        sub_heads_pred, sub_tails_pred, obj_heads_pred, obj_tails_pred = model(
            input_ids, attention_mask, sub_head_idx, sub_tail_idx)
        
        print(f"Subject heads prediction shape: {sub_heads_pred.shape}")
        print(f"Subject tails prediction shape: {sub_tails_pred.shape}")
        print(f"Object heads prediction shape: {obj_heads_pred.shape}")
        print(f"Object tails prediction shape: {obj_tails_pred.shape}")
        
        # 检查预测值范围
        print(f"Subject heads range: [{sub_heads_pred.min():.3f}, {sub_heads_pred.max():.3f}]")
        print(f"Object heads range: [{obj_heads_pred.min():.3f}, {obj_heads_pred.max():.3f}]")
    
    # 测试三元组提取
    print("\n=== 三元组提取测试 ===")
    test_text = "Barack Obama was born in Hawaii."
    predicted_triples = extract_triples(model, tokenizer, test_text, id2rel, device)
    
    print(f"输入文本: {test_text}")
    print(f"预测三元组: {predicted_triples}")
    print(f"期望三元组: [('Barack Obama', 'born_in', 'Hawaii')]")
    
    # 测试文本清理
    print("\n=== 文本清理测试 ===")
    test_tokens = ['Barack', 'Obama']
    cleaned_text = tokenizer.convert_tokens_to_string(test_tokens)
    print(f"原始tokens: {test_tokens}")
    print(f"清理后文本: '{cleaned_text}'")
    
    # 测试subword处理
    test_tokens_subword = ['Barack', 'Ob', '##ama']
    cleaned_subword = tokenizer.convert_tokens_to_string(test_tokens_subword)
    print(f"Subword tokens: {test_tokens_subword}")
    print(f"清理后文本: '{cleaned_subword}'")

def test_edge_cases():
    """测试边界情况"""
    tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
    
    print("\n=== 边界情况测试 ===")
    
    # 测试空文本
    empty_tokens = []
    if empty_tokens:
        empty_text = tokenizer.convert_tokens_to_string(empty_tokens)
        print(f"空tokens: {empty_tokens} -> '{empty_text}'")
    else:
        print("空tokens: [] -> ''")
    
    # 测试单个token
    single_token = ['Hello']
    single_text = tokenizer.convert_tokens_to_string(single_token)
    print(f"单个token: {single_token} -> '{single_text}'")
    
    # 测试特殊token
    special_tokens = ['[CLS]', 'Hello', '[SEP]']
    special_text = tokenizer.convert_tokens_to_string(special_tokens)
    print(f"特殊tokens: {special_tokens} -> '{special_text}'")
    
    # 测试长实体
    long_entity = "United States of America"
    long_tokens = tokenizer.tokenize(long_entity)
    reconstructed = tokenizer.convert_tokens_to_string(long_tokens)
    print(f"长实体: '{long_entity}'")
    print(f"Tokens: {long_tokens}")
    print(f"重构: '{reconstructed}'")
    print(f"是否一致: {long_entity.lower() == reconstructed.lower()}")

if __name__ == "__main__":
    test_full_pipeline()
    test_edge_cases()
