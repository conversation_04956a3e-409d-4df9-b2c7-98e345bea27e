#!/usr/bin/env python3
"""
Debug script to analyze differences between keras and torch versions
"""
import json

def analyze_data_format():
    """Analyze the data format"""
    try:
        # Load NYT dev data
        with open('data/NYT/dev_triples.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("=== Data Format Analysis ===")
        print(f"Total samples: {len(data)}")
        
        # Analyze first few samples
        for i, sample in enumerate(data[:3]):
            print(f"\nSample {i+1}:")
            print(f"Text: {sample['text'][:100]}...")
            print(f"Text length: {len(sample['text'])}")
            print(f"Number of triples: {len(sample['triple_list'])}")
            
            for j, triple in enumerate(sample['triple_list'][:2]):
                print(f"  Triple {j+1}: {triple}")
                # Check if entities exist in text
                subj, rel, obj = triple
                subj_in_text = subj in sample['text']
                obj_in_text = obj in sample['text']
                print(f"    Subject '{subj}' in text: {subj_in_text}")
                print(f"    Object '{obj}' in text: {obj_in_text}")
        
        # Analyze triple statistics
        total_triples = sum(len(sample['triple_list']) for sample in data)
        avg_triples = total_triples / len(data)
        print(f"\nTotal triples: {total_triples}")
        print(f"Average triples per sample: {avg_triples:.2f}")
        
        # Analyze entity lengths
        entity_lengths = []
        for sample in data:
            for triple in sample['triple_list']:
                entity_lengths.append(len(triple[0].split()))  # subject
                entity_lengths.append(len(triple[2].split()))  # object
        
        print(f"Average entity length (words): {sum(entity_lengths)/len(entity_lengths):.2f}")
        print(f"Max entity length: {max(entity_lengths)}")
        
        # Check for multi-word entities
        multi_word_entities = [length for length in entity_lengths if length > 1]
        print(f"Multi-word entities: {len(multi_word_entities)}/{len(entity_lengths)} ({len(multi_word_entities)/len(entity_lengths)*100:.1f}%)")
        
    except Exception as e:
        print(f"Error loading data: {e}")

def analyze_tokenization_differences():
    """Analyze tokenization differences between keras and torch versions"""
    try:
        from transformers import BertTokenizer
        
        print("\n=== Tokenization Analysis ===")
        
        # Test text
        test_text = "Barack Obama was born in Hawaii."
        test_entities = ["Barack Obama", "Hawaii"]
        
        # Torch tokenizer
        torch_tokenizer = BertTokenizer.from_pretrained('bert-base-cased')
        
        print(f"Test text: {test_text}")
        
        # Torch tokenization
        torch_tokens = torch_tokenizer.tokenize(test_text)
        torch_encoding = torch_tokenizer(test_text, max_length=100, truncation=True, padding='max_length')
        
        print(f"\nTorch tokenization:")
        print(f"Tokens: {torch_tokens}")
        print(f"Token count: {len(torch_tokens)}")
        print(f"Input IDs shape: {len(torch_encoding['input_ids'])}")
        
        # Test entity tokenization
        for entity in test_entities:
            entity_tokens = torch_tokenizer.tokenize(entity)
            print(f"Entity '{entity}' -> {entity_tokens}")
            
            # Find in full tokens
            def find_sublist(main_list, sub_list):
                if not sub_list: return -1
                for i in range(1, len(main_list) - len(sub_list) + 1):  # Skip [CLS]
                    if main_list[i:i+len(sub_list)] == sub_list:
                        return i
                return -1
            
            pos = find_sublist(torch_tokens, entity_tokens)
            print(f"  Position in full tokens: {pos}")
        
    except ImportError:
        print("Transformers library not available for tokenization analysis")
    except Exception as e:
        print(f"Error in tokenization analysis: {e}")

def compare_hyperparameters():
    """Compare hyperparameters between versions"""
    print("\n=== Hyperparameter Comparison ===")
    
    keras_params = {
        "batch_size": 6,
        "max_length": 100,
        "learning_rate": 1e-5,
        "epochs": 100,
        "bert_model": "cased_L-12_H-768_A-12 (local)",
        "evaluation": "Partial Match for NYT"
    }
    
    torch_params = {
        "batch_size": 6,  # Updated
        "max_length": 100,  # Updated  
        "learning_rate": 1e-5,
        "epochs": 100,  # Updated
        "bert_model": "bert-base-cased (HuggingFace)",
        "evaluation": "Partial Match for NYT"
    }
    
    print("Keras version:")
    for key, value in keras_params.items():
        print(f"  {key}: {value}")
    
    print("\nTorch version:")
    for key, value in torch_params.items():
        print(f"  {key}: {value}")
    
    print("\nKey differences:")
    for key in keras_params:
        if keras_params[key] != torch_params[key]:
            print(f"  {key}: Keras={keras_params[key]} vs Torch={torch_params[key]}")

def main():
    print("CasRel Keras vs Torch Comparison Analysis")
    print("=" * 50)
    
    analyze_data_format()
    analyze_tokenization_differences()
    compare_hyperparameters()
    
    print("\n=== Recommendations ===")
    print("1. Ensure BERT model compatibility (keras uses local checkpoint, torch uses HuggingFace)")
    print("2. Verify tokenization alignment between versions")
    print("3. Check if partial match evaluation is working correctly")
    print("4. Consider using the same random seed for reproducibility")
    print("5. Verify loss calculation matches exactly")

if __name__ == "__main__":
    main()
